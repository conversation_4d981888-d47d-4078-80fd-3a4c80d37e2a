/**
 * BlackHatWorld 搜索函数
 * 用于构建搜索请求并发送到 BlackHatWorld 论坛
 */

/**
 * 论坛分区ID映射
 */
const FORUM_SECTIONS = {
  // 主要分区
  'making-money': ['12', '15', '66', '71', '72', '50', '68', '69', '76', '65', '106', '158', '167', '208', '132', '13', '83', '219', '93', '85', '125', '205', '102', '141', '75'],
  'black-hat-seo': ['28', '9', '3', '2', '101', '103', '280'],
  'white-hat-seo': ['95', '168', '173', '53', '169', '171', '108', '209', '224', '170', '172', '94', '30'],
  'social-media': ['270', '32', '86', '215', '214', '87', '211', '301', '279', '217', '216', '210', '77'],
  'marketplace': ['73', '203', '310', '193', '194', '195', '308', '196', '197', '198', '18', '112', '43', '199', '206', '200', '302', '201'],
  'programming': ['40', '79', '128', '60', '61', '62', '131', '59', '129', '80', '127', '130', '126'],
  'general': ['23', '242', '261', '262', '263', '265', '299', '24', '25', '26', '226', '31', '303']
};

/**
 * 排序选项
 */
const ORDER_OPTIONS = {
  'relevance': 'relevance',
  'date': 'date', 
  'replies': 'replies'
};

/**
 * 搜索类型
 */
const SEARCH_TYPES = {
  'post': 'post',
  'media': 'xfmg_media',
  'album': 'xfmg_album',
  'comment': 'xfmg_comment'
};

/**
 * BlackHatWorld 搜索类
 */
class BHWSearcher {
  constructor(options = {}) {
    this.baseUrl = 'https://www.blackhatworld.com';
    this.searchEndpoint = '/search';
    this.defaultHeaders = {
      'accept': 'application/json',
      'accept-language': 'zh-CN,zh;q=0.9',
      'content-type': 'multipart/form-data',
      'origin': 'https://www.blackhatworld.com',
      'referer': 'https://www.blackhatworld.com/search/?type=post',
      'x-requested-with': 'XMLHttpRequest',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36'
    };
    
    // 从选项中设置cookies和token
    this.cookies = options.cookies || '';
    this.xfToken = options.xfToken || '';
  }

  /**
   * 设置cookies
   * @param {string} cookies - Cookie字符串
   */
  setCookies(cookies) {
    this.cookies = cookies;
  }

  /**
   * 设置XF Token
   * @param {string} token - XF Token
   */
  setXfToken(token) {
    this.xfToken = token;
  }

  /**
   * 构建搜索参数
   * @param {Object} searchOptions - 搜索选项
   * @returns {FormData} - 构建好的FormData对象
   */
  buildSearchParams(searchOptions = {}) {
    const formData = new FormData();
    
    // 必需的基础参数
    // formData.append('_xfToken', this.xfToken);
    formData.append('_xfResponseType', 'json');
    formData.append('_xfWithData', '1');
    formData.append('_xfRequestUri', '/search/?type=post');
    
    // 搜索关键词
    if (searchOptions.keywords) {
      formData.append('keywords', searchOptions.keywords);
    }
    
    // 用户筛选
    if (searchOptions.users) {
      formData.append('c[users]', searchOptions.users);
    }
    
    // 时间筛选
    if (searchOptions.newerThan) {
      formData.append('c[newer_than]', searchOptions.newerThan);
    }
    
    if (searchOptions.olderThan) {
      formData.append('c[older_than]', searchOptions.olderThan);
    }
    
    // 最小回复数
    const minReplies = searchOptions.minReplies || 0;
    formData.append('c[min_reply_count]', minReplies.toString());
    
    // 论坛分区
    if (searchOptions.sections && Array.isArray(searchOptions.sections)) {
      // 如果传入的是预定义的分区名称，转换为ID
      let nodeIds = [];
      searchOptions.sections.forEach(section => {
        if (FORUM_SECTIONS[section]) {
          nodeIds = nodeIds.concat(FORUM_SECTIONS[section]);
        } else if (typeof section === 'string' && /^\d+$/.test(section)) {
          // 如果是数字ID字符串，直接使用
          nodeIds.push(section);
        }
      });
      
      // 添加节点ID
      nodeIds.forEach(nodeId => {
        formData.append('c[nodes][]', nodeId);
      });
    }
    
    // 是否搜索子论坛
    const childNodes = searchOptions.childNodes !== false ? '1' : '0';
    formData.append('c[child_nodes]', childNodes);
    
    // 排序方式
    const order = ORDER_OPTIONS[searchOptions.order] || 'relevance';
    formData.append('order', order);
    
    // 是否按线程分组显示
    if (searchOptions.grouped) {
      formData.append('grouped', '1');
    }
    
    // 搜索类型
    const searchType = SEARCH_TYPES[searchOptions.searchType] || 'post';
    formData.append('search_type', searchType);
    
    // 只搜索标题
    if (searchOptions.titleOnly) {
      formData.append('c[title_only]', '1');
    }
    
    // 添加第二个token（从curl请求中看到的）
    // formData.append('_xfToken', this.xfToken);
    
    return formData;
  }

  /**
   * 执行搜索
   * @param {Object} searchOptions - 搜索选项
   * @returns {Promise<Object>} - 搜索结果
   */
  async search(searchOptions = {}) {
    if (!this.xfToken) {
      throw new Error('XF Token is required. Please set it using setXfToken()');
    }

    const formData = this.buildSearchParams(searchOptions);
    
    const requestOptions = {
      method: 'POST',
      headers: {
        ...this.defaultHeaders,
        // 'cookie': this.cookies
      },
      body: formData
    };

    // 移除content-type，让浏览器自动设置multipart boundary
    delete requestOptions.headers['content-type'];

      try {
          console.log("requestOptions", requestOptions, this.baseUrl + this.searchEndpoint);
      const response = await fetch(this.baseUrl + this.searchEndpoint, requestOptions);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Search failed:', error);
      throw error;
    }
  }

  /**
   * 便捷搜索方法 - 搜索关键词
   * @param {string} keywords - 搜索关键词
   * @param {Object} options - 额外选项
   * @returns {Promise<Object>} - 搜索结果
   */
  async searchKeywords(keywords, options = {}) {
    return this.search({
      keywords,
      ...options
    });
  }

  /**
   * 便捷搜索方法 - 在指定分区搜索
   * @param {string} keywords - 搜索关键词
   * @param {Array} sections - 论坛分区
   * @param {Object} options - 额外选项
   * @returns {Promise<Object>} - 搜索结果
   */
  async searchInSections(keywords, sections, options = {}) {
    return this.search({
      keywords,
      sections,
      ...options
    });
  }

  /**
   * 便捷搜索方法 - 搜索最近的热门帖子
   * @param {string} keywords - 搜索关键词
   * @param {number} days - 最近天数
   * @param {number} minReplies - 最小回复数
   * @param {Object} options - 额外选项
   * @returns {Promise<Object>} - 搜索结果
   */
  async searchRecentPopular(keywords, days = 7, minReplies = 5, options = {}) {
    const newerThan = new Date();
    newerThan.setDate(newerThan.getDate() - days);
    
    return this.search({
      keywords,
      newerThan: newerThan.toISOString().split('T')[0],
      minReplies,
      order: 'replies',
      ...options
    });
  }
}

/**
 * 简化的搜索函数
 * @param {Object} config - 配置对象
 * @param {Object} searchOptions - 搜索选项
 * @returns {Promise<Object>} - 搜索结果
 */
async function bhwSearch(config, searchOptions) {
  const searcher = new BHWSearcher({
    cookies: config.cookies,
    xfToken: config.xfToken
  });
  
  return await searcher.search(searchOptions);
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    BHWSearcher,
    bhwSearch,
    FORUM_SECTIONS,
    ORDER_OPTIONS,
    SEARCH_TYPES
  };
}

// 浏览器环境下的全局导出
if (typeof window !== 'undefined') {
  window.BHWSearcher = BHWSearcher;
  window.bhwSearch = bhwSearch;
  window.BHW_FORUM_SECTIONS = FORUM_SECTIONS;
  window.BHW_ORDER_OPTIONS = ORDER_OPTIONS;
  window.BHW_SEARCH_TYPES = SEARCH_TYPES;
}


 // 1. 基础搜索
 const searcher = new BHWSearcher({
   cookies: 'your_cookies_here',
   xfToken: 'your_xf_token_here'
 });
 
 // 搜索关键词
const results = await searcher.searchKeywords('content farm');
 console.log("results", results);

/**
 * 使用示例：
 * 
 * // 1. 基础搜索
 * const searcher = new BHWSearcher({
 *   cookies: 'your_cookies_here',
 *   xfToken: 'your_xf_token_here'
 * });
 * 
 * // 搜索关键词
 * const results = await searcher.searchKeywords('content farm');
 * 
 * // 2. 高级搜索
 * const advancedResults = await searcher.search({
 *   keywords: 'content farm',
 *   sections: ['making-money', 'black-hat-seo'],
 *   newerThan: '2025-01-20',
 *   minReplies: 8,
 *   order: 'relevance',
 *   grouped: true
 * });
 * 
 * // 3. 搜索最近的热门帖子
 * const popularResults = await searcher.searchRecentPopular('SEO', 30, 10);
 * 
 * // 4. 使用简化函数
 * const simpleResults = await bhwSearch(
 *   { cookies: 'cookies', xfToken: 'token' },
 *   { keywords: 'affiliate marketing', sections: ['making-money'] }
 * );
 */