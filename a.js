import axios from 'axios';

// 请求体数据
const requestBody = "------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1753602185,dc8dadea6a4040c4cce1a37f67b28f6b\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"keywords\"\r\n\r\ncontent farm\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[users]\"\r\n\r\n\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[newer_than]\"\r\n\r\n\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[older_than]\"\r\n\r\n\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[min_reply_count]\"\r\n\r\n0\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[child_nodes]\"\r\n\r\n1\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"order\"\r\n\r\nrelevance\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"search_type\"\r\n\r\npost\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfResponseType\"\r\n\r\njson\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfWithData\"\r\n\r\n1\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfRequestUri\"\r\n\r\n/search/?type=post\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1753602185,dc8dadea6a4040c4cce1a37f67b28f6b\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ--\r\n";

// 请求头配置
const requestHeaders = {
  "accept": "application/json",
  "accept-language": "zh-CN,zh;q=0.9",
  "content-type": "multipart/form-data; boundary=----WebKitFormBoundaryxH9AH85n8asqdblZ",
  "priority": "u=1, i",
  "sec-ch-ua": "\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"",
  "sec-ch-ua-arch": "\"x86\"",
  "sec-ch-ua-bitness": "\"64\"",
  "sec-ch-ua-full-version": "\"140.0.7320.0\"",
  "sec-ch-ua-full-version-list": "\"Chromium\";v=\"140.0.7320.0\", \"Not=A?Brand\";v=\"********\", \"Google Chrome\";v=\"140.0.7320.0\"",
  "sec-ch-ua-mobile": "?0",
  "sec-ch-ua-model": "\"\"",
  "sec-ch-ua-platform": "\"Windows\"",
  "sec-ch-ua-platform-version": "\"10.0.0\"",
  "sec-fetch-dest": "empty",
  "sec-fetch-mode": "cors",
  "sec-fetch-site": "same-origin",
  "x-requested-with": "XMLHttpRequest",
  "cookie": "_ga=GA1.1.968538537.1753585988; xf_notice_dismiss=-1; xf_csrf=yd8XOJBIXCDMTDcQ; xf_session=JxRraKR8gNtWtAccXGnX0sRlOi0jjAoh; _ga_VH2PZEKYEE=GS2.1.s1753601117$o3$g1$t1753603868$j58$l0$h0",
  "Referer": "https://www.blackhatworld.com/search/22848992/?q=content+farm&t=post&o=relevance"
};

// ===== AXIOS 请求 =====
console.log('开始 AXIOS 请求...');
axios({
  method: 'POST',
  url: "https://www.blackhatworld.com/search/search",
  data: requestBody,
  headers: requestHeaders,
  timeout: 30000,
  maxRedirects: 5,
  validateStatus: function (status) {
    return status < 500; // 接受所有小于500的状态码
  }
})
.then(response => {
  console.log('AXIOS 响应状态:', response.status);
  console.log('AXIOS 成功! 数据类型:', typeof response.data);
  if (typeof response.data === 'string') {
    console.log('AXIOS 数据长度:', response.data.length);
    console.log('AXIOS 前100字符:', response.data.substring(0, 100));
  } else {
    console.log('AXIOS 数据:', response.data);
  }
})
.catch(error => {
  console.error('AXIOS 失败:', error.message);
  if (error.response) {
    console.error('AXIOS 状态码:', error.response.status);
    console.error('AXIOS 状态文本:', error.response.statusText);
  }
  if (error.code) {
    console.error('AXIOS 错误代码:', error.code);
  }
});




// ===== FETCH 请求 (对比版本) =====
console.log('开始 FETCH 请求...');
fetch("https://www.blackhatworld.com/search/search", {
  headers: requestHeaders,
  body: requestBody,
  method: "POST"
})
.then(response => {
  console.log('FETCH 响应状态:', response.status);
  console.log('FETCH 响应状态文本:', response.statusText);

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
})
.then(data => {
  console.log('FETCH 成功! 数据类型:', typeof data);
  if (typeof data === 'string') {
    console.log('FETCH 数据长度:', data.length);
    console.log('FETCH 前100字符:', data.substring(0, 100));
  } else {
    console.log('FETCH 数据:', data);
  }
})
.catch(error => {
  console.error('FETCH 失败:', error.message);
  if (error.cause) {
    console.error('FETCH 错误原因:', error.cause.code);
  }
});