import axios from 'axios';

const instance = axios.create({
  headers: {
    'Content-Type': 'application/json'
  },
  proxy: {
    protocol: 'https',
    host: '*************',
    port: 6114,
    auth: {
      username: 'vsodmy<PERSON>',
      password: 'kn0t2g37ncpe'
    }
  }
});

// instance('http://ipv4.webshare.io/',{
 
// }).then(function(data){
//   console.log(data.data); 
// }, function(err){ 
//   console.error(err); 
// });
// 配置代理

 instance.post("https://www.blackhatworld.com/search", {
  "headers": {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "multipart/form-data; boundary=----WebKitFormBoundary7oXcxNywQBujcjHu",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"140.0.7316.0\"",
    "sec-ch-ua-full-version-list": "\"Chromium\";v=\"140.0.7316.0\", \"Not=A?Brand\";v=\"********\", \"Google Chrome\";v=\"140.0.7316.0\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"10.0.0\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "x-requested-with": "XMLHttpRequest",
    "cookie": "xf_csrf=bIDUyMnr77yqYHqb; xf_session=kjDMTctXnksGOPAiq-e2mtQbIruwateu; _ga=GA1.1.968538537.1753585988; xf_notice_dismiss=-1; _ga_VH2PZEKYEE=GS2.1.s1753595178$o2$g1$t1753596446$j33$l0$h0",
    "Referer": "https://www.blackhatworld.com/search/?type=post"
  },
  "body": "------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1753596355,f22841bb34aa9207196ee571cd85c6ab\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"keywords\"\r\n\r\ncontent farm\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"c[users]\"\r\n\r\n\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"c[newer_than]\"\r\n\r\n2025-01-27\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"c[older_than]\"\r\n\r\n\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"c[min_reply_count]\"\r\n\r\n8\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"c[nodes][]\"\r\n\r\n\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"c[child_nodes]\"\r\n\r\n1\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"order\"\r\n\r\nrelevance\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"search_type\"\r\n\r\npost\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"_xfResponseType\"\r\n\r\njson\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"_xfWithData\"\r\n\r\n1\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data; name=\"_xfRequestUri\"\r\n\r\n/search/?type=post\r\n------WebKitFormBoundary7oXcxNywQBujcjHu\r\nContent-Disposition: form-data;",

}).then(res => {
  console.log(res.data);
}).catch(error => {
  console.error('请求失败:', error);
});  