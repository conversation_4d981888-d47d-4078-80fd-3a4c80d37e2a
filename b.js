import axios from 'axios';

class ScrapingAntProxy {
  constructor(apiKey) {
    this.apiKey = apiKey;
    // this.baseURL =  'https://api.scrapingant.com/v2/general';
    const url = 'https://api.scrapingdog.com/scrape?api_key=5e5a97e5b1ca5b194f42da86c5e27dd646&url=https://www.blackhatworld.com/search/search';
    this.baseURL = url
  }

  async postRequest(targetUrl, data, options = {}) {
    try {
      // 构建 ScrapingAnt 的自定义头部
      const antHeaders = {};
      if (options.headers) {
        Object.keys(options.headers).forEach(key => {
          antHeaders[`Ant-${key}`] = options.headers[key];
        });
      }

      const config = {
        method: 'POST',
        url: this.baseURL,
        params: {
          'x-api-key': this.apiKey,
          'url': targetUrl,
          // 可选参数
          'proxy_type': options.proxyType || 'datacenter',
          'proxy_country': options.proxyCountry || 'US',
          'browser': options.browser || false
        },
        data: data,
        headers: {
          'Content-Type': 'application/json',
          // ScrapingAnt 自定义头部
          'Ant-Content-Type': options.contentType || 'application/json',
          ...antHeaders
        },
        timeout: options.timeout || 30000
      };

      const response = await axios(config);
      return {
        success: true,
        data: response.data,
        status: response.status,
        headers: response.headers
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      };
    }
  }
}

// BlackHatWorld 搜索请求
async function main() {
  const proxy = new ScrapingAntProxy('********************************');

  // 构建 multipart/form-data 请求体
  const formData = "------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1753602185,dc8dadea6a4040c4cce1a37f67b28f6b\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"keywords\"\r\n\r\ncontent farm\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[users]\"\r\n\r\n\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[newer_than]\"\r\n\r\n\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[older_than]\"\r\n\r\n\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[min_reply_count]\"\r\n\r\n0\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"c[child_nodes]\"\r\n\r\n1\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"order\"\r\n\r\nrelevance\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"search_type\"\r\n\r\npost\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfResponseType\"\r\n\r\njson\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfWithData\"\r\n\r\n1\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfRequestUri\"\r\n\r\n/search/?type=post\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1753602185,dc8dadea6a4040c4cce1a37f67b28f6b\r\n------WebKitFormBoundaryxH9AH85n8asqdblZ--\r\n";

    const result = await proxy.postRequest(
      'https://api.scrapingdog.com/scrape?api_key=5e5a97e5b1ca5b194f42da86c5e27dd646&url=https://www.blackhatworld.com/search/search',
    // 'https://www.blackhatworld.com/search/search',
    formData,
    {
      contentType: 'multipart/form-data; boundary=----WebKitFormBoundaryxH9AH85n8asqdblZ',
      proxyCountry: 'US', // 改用英国代理
      proxyType: 'residential', // 使用住宅代理
      browser: true, // 启用浏览器渲染
      timeout: 65000,
      headers: {
       "accept": "application/json",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "multipart/form-data; boundary=----WebKitFormBoundaryxH9AH85n8asqdblZ",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"",
        "sec-ch-ua-arch": "\"x86\"",
        "sec-ch-ua-bitness": "\"64\"",
        "sec-ch-ua-full-version": "\"140.0.7320.0\"",
        "sec-ch-ua-full-version-list": "\"Chromium\";v=\"140.0.7320.0\", \"Not=A?Brand\";v=\"********\", \"Google Chrome\";v=\"140.0.7320.0\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-model": "\"\"",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-ch-ua-platform-version": "\"10.0.0\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        "cookie": "_ga=GA1.1.968538537.1753585988; xf_notice_dismiss=-1; xf_csrf=yd8XOJBIXCDMTDcQ; xf_session=JxRraKR8gNtWtAccXGnX0sRlOi0jjAoh; _ga_VH2PZEKYEE=GS2.1.s1753601117$o3$g1$t1753602325$j59$l0$h0",
        "Referer": "https://www.blackhatworld.com/search/?type=post"
      }
    }
  );

  if (result.success) {
    console.log('请求成功:', result.data);
  } else {
    console.error('请求失败:', result.error);
    console.error('状态码:', result.status);
    console.error('响应数据:', result.data);
  }
}

main();