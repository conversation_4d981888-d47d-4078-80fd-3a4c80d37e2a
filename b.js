import axios from 'axios';

class ScrapingAntProxy {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseURL = 'https://api.scrapingant.com/v2/general';
  }

  async postRequest(targetUrl, data, options = {}) {
    try {
      const config = {
        method: 'POST',
        url: this.baseURL,
        params: {
          'x-api-key': this.apiKey,
          'url': targetUrl,
          // 可选参数
          'proxy_type': options.proxyType || 'datacenter',
          'proxy_country': options.proxyCountry || 'US',
          'browser': options.browser || false
        },
        data: data,
        headers: {
          'Content-Type': options.contentType || 'application/json',
          // ScrapingAnt 自定义头部前缀
          'Ant-Content-Type': options.contentType || 'application/json',
          ...options.headers
        },
        timeout: options.timeout || 30000
      };

      const response = await axios(config);
      return {
        success: true,
        data: response.data,
        status: response.status,
        headers: response.headers
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      };
    }
  }
}

// 使用示例
async function main() {
  const proxy = new ScrapingAntProxy('********************************');
  
  // POST JSON 数据
  const result = await proxy.postRequest(
    'https://httpbin.org/post',
    {
      name: 'John Doe',
      email: '<EMAIL>',
      message: 'Hello World'
    },
    {
      contentType: 'application/json',
      proxyCountry: 'US',
      timeout: 15000
    }
  );
  
  console.log('Result:', result);
}

main();